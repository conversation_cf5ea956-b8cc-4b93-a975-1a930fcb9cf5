.gitignore
.augment-guidelines
lib/firebase_options.dart

# Flutter/Dart/Pub related
.dart_tool/
.packages
.pub-cache/
build/
.flutter-plugins
.flutter-plugins-dependencies
.metadata
firebase_app_id_file.json

# Environment files (API keys, secrets)
.env
*.env

# Firebase config files (DO NOT UPLOAD)
android/app/google-services.json
ios/Runner/GoogleService-Info.plist

# Android specific
android/.gradle/
android/captures/
android/key.properties
android/local.properties

# iOS specific
ios/Flutter/Flutter.framework
ios/Flutter/Flutter.podspec
ios/Runner.xcworkspace/
ios/Pods/
ios/.symlinks/
ios/Flutter/ephemeral/

# macOS specific
macos/Flutter/Flutter.framework
macos/Flutter/Flutter.podspec
macos/.symlinks/
macos/Flutter/ephemeral/

# Windows/Linux/Web
linux/flutter/ephemeral/
windows/flutter/ephemeral/
web/.dart_tool/
web/index.html

# IDEs
.idea/
.vscode/
*.iml

# Misc
*.log
*.lock
*.tmp
*.swp
*.DS_Store
*.class
*.jar
*.keystore
