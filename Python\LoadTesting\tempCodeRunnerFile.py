from locust import HttpUser, task, between

class FlutterAppUser(HttpUser):
    wait_time = between(1, 3)  # 1 to 3 seconds between requests

    @task
    def test_home_endpoint(self):
        # Replace this with your actual endpoint
        self.client.get("/api/home")

    @task(2)  # This task will be run twice as often as the previous one
    def test_login(self):
        self.client.post("/api/login", json={
            "email": "<EMAIL>",
            "password": "test123"
        })

    @task
    def test_fetch_items(self):
        self.client.get("/api/items")
